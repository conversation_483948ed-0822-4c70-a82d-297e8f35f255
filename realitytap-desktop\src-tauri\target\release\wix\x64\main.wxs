<?if $(sys.B<PERSON><PERSON>ARCH)="x86"?>
    <?define Win64 = "no" ?>
    <?define PlatformProgramFilesFolder = "ProgramFilesFolder" ?>
<?elseif $(sys.BUILDARCH)="x64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?elseif $(sys.BUILDARCH)="arm64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?else?>
    <?error Unsupported value of sys.BUILDARCH=$(sys.BUILDARCH)?>
<?endif?>

<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Product
            Id="*"
            Name="RealityTap Haptics Studio"
            UpgradeCode="dfa82347-b28e-549b-8f38-eb7559a97d0a"
            Language="!(loc.TauriLanguage)"
            Manufacturer="AWA"
            Version="1.0.8">

        <Package Id="*"
                 Keywords="Installer"
                 InstallerVersion="450"
                 Languages="0"
                 Compressed="yes"
                 InstallScope="perMachine"
                 SummaryCodepage="!(loc.TauriCodepage)"/>

        <!-- https://docs.microsoft.com/en-us/windows/win32/msi/reinstallmode -->
        <!-- reinstall all files; rewrite all registry entries; reinstall all shortcuts -->
        <Property Id="REINSTALLMODE" Value="amus" />

        <!-- Auto launch app after installation, useful for passive mode which usually used in updates -->
        <Property Id="AUTOLAUNCHAPP" Secure="yes" />
        <!-- Property to forward cli args to the launched app to not lose those of the pre-update instance -->
        <Property Id="LAUNCHAPPARGS" Secure="yes" />

            <MajorUpgrade Schedule="afterInstallInitialize" AllowDowngrades="yes" />

        <InstallExecuteSequence>
            <RemoveShortcuts>Installed AND NOT UPGRADINGPRODUCTCODE</RemoveShortcuts>
        </InstallExecuteSequence>

        <Media Id="1" Cabinet="app.cab" EmbedCab="yes" />


        <Icon Id="ProductIcon" SourceFile="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\release\resources\icon.ico"/>
        <Property Id="ARPPRODUCTICON" Value="ProductIcon" />
        <Property Id="ARPNOREPAIR" Value="yes" Secure="yes" />      <!-- Remove repair -->
        <SetProperty Id="ARPNOMODIFY" Value="1" After="InstallValidate" Sequence="execute"/>


        <Property Id="INSTALLDIR">
          <!-- First attempt: Search for "InstallDir" -->
          <RegistrySearch Id="PrevInstallDirWithName" Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Name="InstallDir" Type="raw" />

          <!-- Second attempt: If the first fails, search for the default key value (this is how the nsis installer currently stores the path) -->
          <RegistrySearch Id="PrevInstallDirNoName" Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Type="raw" />
        </Property>

        <!-- launch app checkbox -->
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOXTEXT" Value="!(loc.LaunchApp)" />
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOX" Value="1"/>
        <CustomAction Id="LaunchApplication" Impersonate="yes" FileKey="Path" ExeCommand="[LAUNCHAPPARGS]" Return="asyncNoWait" />

        <UI>
            <!-- launch app checkbox -->
            <Publish Dialog="ExitDialog" Control="Finish" Event="DoAction" Value="LaunchApplication">WIXUI_EXITDIALOGOPTIONALCHECKBOX = 1 and NOT Installed</Publish>

            <Property Id="WIXUI_INSTALLDIR" Value="INSTALLDIR" />

            <!-- Skip license dialog -->
            <Publish Dialog="WelcomeDlg"
                     Control="Next"
                     Event="NewDialog"
                     Value="InstallDirDlg"
                     Order="2">1</Publish>
            <Publish Dialog="InstallDirDlg"
                     Control="Back"
                     Event="NewDialog"
                     Value="WelcomeDlg"
                     Order="2">1</Publish>
        </UI>

        <UIRef Id="WixUI_InstallDir" />

        <Directory Id="TARGETDIR" Name="SourceDir">
            <Directory Id="DesktopFolder" Name="Desktop">
                <Component Id="ApplicationShortcutDesktop" Guid="*">
                    <Shortcut Id="ApplicationDesktopShortcut" Name="RealityTap Haptics Studio" Description="Runs RealityTap Haptics Studio" Target="[!Path]" WorkingDirectory="INSTALLDIR" />
                    <RemoveFolder Id="DesktopFolder" On="uninstall" />
                    <RegistryValue Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Name="Desktop Shortcut" Type="integer" Value="1" KeyPath="yes" />
                </Component>
            </Directory>
            <Directory Id="$(var.PlatformProgramFilesFolder)" Name="PFiles">
                <Directory Id="INSTALLDIR" Name="RealityTap Haptics Studio"/>
            </Directory>
            <Directory Id="ProgramMenuFolder">
                <Directory Id="ApplicationProgramsFolder" Name="RealityTap Haptics Studio"/>
            </Directory>
        </Directory>

        <DirectoryRef Id="INSTALLDIR">
            <Component Id="RegistryEntries" Guid="*">
                <RegistryKey Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio">
                    <RegistryValue Name="InstallDir" Type="string" Value="[INSTALLDIR]" KeyPath="yes" />
                </RegistryKey>
                <!-- Change the Root to HKCU for perUser installations -->
</Component>
            <Component Id="Path" Guid="0147ef6a-fc30-5fd9-bd41-312c6ae15b1a" Win64="$(var.Win64)">
                <File Id="Path" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\release\realitytap_studio.exe" KeyPath="yes" Checksum="yes"/>
</Component>
            <Component Id="I599ecd7c5e574146b808b943fb523366" Guid="8967ab12-473a-44d7-b5f3-604ebbef5484" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I599ecd7c5e574146b808b943fb523366" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\librtcore.dll" /></Component><Component Id="I53f585ff1bd74e4286acc47a28459df6" Guid="d7351de8-7809-4f00-b4e6-f2269a51bba3" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I53f585ff1bd74e4286acc47a28459df6" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\librtssl.dll" /></Component><Component Id="I8b4d8820ac7e45fc8bee1a7ff7ee142f" Guid="a005adfc-9c8d-4c55-964f-b7263d98f149" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I8b4d8820ac7e45fc8bee1a7ff7ee142f" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\librtutils.dll" /></Component><Component Id="I8d9a039e3dff4c3b8fc398659cf2aba5" Guid="4012ac12-40fb-4a6f-8a61-69b8ff356ab4" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I8d9a039e3dff4c3b8fc398659cf2aba5" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\libgcc_s_seh-1.dll" /></Component><Component Id="Iba7c1fad8f2e4783ba4e5a7d84626713" Guid="64080d8d-f888-4ea4-9d5c-faeb0c7ac650" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iba7c1fad8f2e4783ba4e5a7d84626713" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\libstdc++-6.dll" /></Component><Component Id="If993d204c8f146f585af2151077a63f5" Guid="2aa93d07-5951-4c29-ac07-bdba4b55b4bc" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_If993d204c8f146f585af2151077a63f5" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\libwinpthread-1.dll" /></Component><Component Id="I6be7855100b146b69c637bdcab072f56" Guid="c66b9020-6086-44a0-8253-40a47caaea30" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I6be7855100b146b69c637bdcab072f56" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\ffmpeg.exe" /></Component><Component Id="I1e34f0c3939e4852bb10186f6129ed91" Guid="0b3f3e51-fcdc-4734-840c-d958be3bcea7" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I1e34f0c3939e4852bb10186f6129ed91" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\ffprobe.exe" /></Component><Directory Id="I8cfb16103010415caff1547ed34700b7" Name="motors"><Component Id="I35e0e2cf2dbf48c0a0f18e14b3cbd7a7" Guid="3a55d283-a05b-450b-9440-3d2cb9049ccf" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I35e0e2cf2dbf48c0a0f18e14b3cbd7a7" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\motors\LRA_0809_normal_170Hz.conf" /></Component><Component Id="Ib4aa8631ba354386a03f77aa96809c8b" Guid="59d2d89d-8577-449b-830e-f8485b8abf73" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ib4aa8631ba354386a03f77aa96809c8b" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\motors\LRA_0809_pro_170Hz.conf" /></Component><Component Id="Icfc92084ec804945a636ac6ae37cc607" Guid="8be72209-0136-4aee-9c50-1ae237b24faa" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Icfc92084ec804945a636ac6ae37cc607" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\motors\LRA_0916_normal_170Hz.conf" /></Component></Directory>
            <Component Id="CMP_UninstallShortcut" Guid="*">

                <Shortcut Id="UninstallShortcut"
						  Name="Uninstall RealityTap Haptics Studio"
						  Description="Uninstalls RealityTap Haptics Studio"
						  Target="[System64Folder]msiexec.exe"
						  Arguments="/x [ProductCode]" />

				<RemoveFolder Id="INSTALLDIR"
							  On="uninstall" />

				<RegistryValue Root="HKCU"
							   Key="Software\AWA\RealityTap Haptics Studio"
							   Name="Uninstaller Shortcut"
							   Type="integer"
							   Value="1"
							   KeyPath="yes" />
            </Component>
        </DirectoryRef>

        <DirectoryRef Id="ApplicationProgramsFolder">
            <Component Id="ApplicationShortcut" Guid="*">
                <Shortcut Id="ApplicationStartMenuShortcut"
                    Name="RealityTap Haptics Studio"
                    Description="Runs RealityTap Haptics Studio"
                    Target="[!Path]"
                    Icon="ProductIcon"
                    WorkingDirectory="INSTALLDIR">
                    <ShortcutProperty Key="System.AppUserModel.ID" Value="com.awa.realitytap.desktop"/>
                </Shortcut>
                <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall"/>
                <RegistryValue Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Name="Start Menu Shortcut" Type="integer" Value="1" KeyPath="yes"/>
           </Component>
        </DirectoryRef>

<Feature
                Id="MainProgram"
                Title="Application"
                Description="!(loc.InstallAppFeature)"
                Level="1"
                ConfigurableDirectory="INSTALLDIR"
                AllowAdvertise="no"
                Display="expand"
                Absent="disallow">

            <ComponentRef Id="RegistryEntries"/>

<ComponentRef Id="I599ecd7c5e574146b808b943fb523366"/>
<ComponentRef Id="I53f585ff1bd74e4286acc47a28459df6"/>
<ComponentRef Id="I8b4d8820ac7e45fc8bee1a7ff7ee142f"/>
<ComponentRef Id="I8d9a039e3dff4c3b8fc398659cf2aba5"/>
<ComponentRef Id="Iba7c1fad8f2e4783ba4e5a7d84626713"/>
<ComponentRef Id="If993d204c8f146f585af2151077a63f5"/>
<ComponentRef Id="I6be7855100b146b69c637bdcab072f56"/>
<ComponentRef Id="I1e34f0c3939e4852bb10186f6129ed91"/>
<ComponentRef Id="I35e0e2cf2dbf48c0a0f18e14b3cbd7a7"/>
<ComponentRef Id="Ib4aa8631ba354386a03f77aa96809c8b"/>
<ComponentRef Id="Icfc92084ec804945a636ac6ae37cc607"/>

            <Feature Id="ShortcutsFeature"
                Title="Shortcuts"
                Level="1">
                <ComponentRef Id="Path"/>
                <ComponentRef Id="CMP_UninstallShortcut" />
                <ComponentRef Id="ApplicationShortcut" />
                <ComponentRef Id="ApplicationShortcutDesktop" />
            </Feature>

            <Feature
                Id="Environment"
                Title="PATH Environment Variable"
                Description="!(loc.PathEnvVarFeature)"
                Level="1"
                Absent="allow">
            <ComponentRef Id="Path"/>
</Feature>
        </Feature>

        <Feature Id="External" AllowAdvertise="no" Absent="disallow">
</Feature>

        <!-- WebView2 -->
        <Property Id="WVRTINSTALLED">
            <RegistrySearch Id="WVRTInstalledSystem" Root="HKLM" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw" Win64="no" />
            <RegistrySearch Id="WVRTInstalledUser" Root="HKCU" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw"/>
        </Property>

        <CustomAction Id='DownloadAndInvokeBootstrapper' Directory="INSTALLDIR" Execute="deferred" ExeCommand='powershell.exe -NoProfile -windowstyle hidden try [\{] [\[]Net.ServicePointManager[\]]::SecurityProtocol = [\[]Net.SecurityProtocolType[\]]::Tls12 [\}] catch [\{][\}]; Invoke-WebRequest -Uri "https://go.microsoft.com/fwlink/p/?LinkId=2124703" -OutFile "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" ; Start-Process -FilePath "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" -ArgumentList (&apos;/silent&apos;, &apos;/install&apos;) -Wait' Return='check'/>
        <InstallExecuteSequence>
            <Custom Action='DownloadAndInvokeBootstrapper' Before='InstallFinalize'>
                <![CDATA[NOT(REMOVE OR WVRTINSTALLED)]]>
            </Custom>
        </InstallExecuteSequence>

        <!-- Embedded webview bootstrapper mode -->

        <!-- Embedded offline installer -->



        <InstallExecuteSequence>
          <Custom Action="LaunchApplication" After="InstallFinalize">AUTOLAUNCHAPP AND NOT Installed</Custom>
        </InstallExecuteSequence>

        <SetProperty Id="ARPINSTALLLOCATION" Value="[INSTALLDIR]" After="CostFinalize"/>
    </Product>
</Wix>
