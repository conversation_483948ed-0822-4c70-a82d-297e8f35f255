<?if $(sys.B<PERSON><PERSON>ARCH)="x86"?>
    <?define Win64 = "no" ?>
    <?define PlatformProgramFilesFolder = "ProgramFilesFolder" ?>
<?elseif $(sys.BUILDARCH)="x64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?elseif $(sys.BUILDARCH)="arm64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?else?>
    <?error Unsupported value of sys.BUILDARCH=$(sys.BUILDARCH)?>
<?endif?>

<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Product
            Id="*"
            Name="RealityTap Haptics Studio"
            UpgradeCode="dfa82347-b28e-549b-8f38-eb7559a97d0a"
            Language="!(loc.TauriLanguage)"
            Manufacturer="AWA"
            Version="1.0.8">

        <Package Id="*"
                 Keywords="Installer"
                 InstallerVersion="450"
                 Languages="0"
                 Compressed="yes"
                 InstallScope="perMachine"
                 SummaryCodepage="!(loc.TauriCodepage)"/>

        <!-- https://docs.microsoft.com/en-us/windows/win32/msi/reinstallmode -->
        <!-- reinstall all files; rewrite all registry entries; reinstall all shortcuts -->
        <Property Id="REINSTALLMODE" Value="amus" />

        <!-- Auto launch app after installation, useful for passive mode which usually used in updates -->
        <Property Id="AUTOLAUNCHAPP" Secure="yes" />
        <!-- Property to forward cli args to the launched app to not lose those of the pre-update instance -->
        <Property Id="LAUNCHAPPARGS" Secure="yes" />

            <MajorUpgrade Schedule="afterInstallInitialize" AllowDowngrades="yes" />

        <InstallExecuteSequence>
            <RemoveShortcuts>Installed AND NOT UPGRADINGPRODUCTCODE</RemoveShortcuts>
        </InstallExecuteSequence>

        <Media Id="1" Cabinet="app.cab" EmbedCab="yes" />


        <Icon Id="ProductIcon" SourceFile="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\release\resources\icon.ico"/>
        <Property Id="ARPPRODUCTICON" Value="ProductIcon" />
        <Property Id="ARPNOREPAIR" Value="yes" Secure="yes" />      <!-- Remove repair -->
        <SetProperty Id="ARPNOMODIFY" Value="1" After="InstallValidate" Sequence="execute"/>


        <Property Id="INSTALLDIR">
          <!-- First attempt: Search for "InstallDir" -->
          <RegistrySearch Id="PrevInstallDirWithName" Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Name="InstallDir" Type="raw" />

          <!-- Second attempt: If the first fails, search for the default key value (this is how the nsis installer currently stores the path) -->
          <RegistrySearch Id="PrevInstallDirNoName" Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Type="raw" />
        </Property>

        <!-- launch app checkbox -->
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOXTEXT" Value="!(loc.LaunchApp)" />
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOX" Value="1"/>
        <CustomAction Id="LaunchApplication" Impersonate="yes" FileKey="Path" ExeCommand="[LAUNCHAPPARGS]" Return="asyncNoWait" />

        <UI>
            <!-- launch app checkbox -->
            <Publish Dialog="ExitDialog" Control="Finish" Event="DoAction" Value="LaunchApplication">WIXUI_EXITDIALOGOPTIONALCHECKBOX = 1 and NOT Installed</Publish>

            <Property Id="WIXUI_INSTALLDIR" Value="INSTALLDIR" />

            <!-- Skip license dialog -->
            <Publish Dialog="WelcomeDlg"
                     Control="Next"
                     Event="NewDialog"
                     Value="InstallDirDlg"
                     Order="2">1</Publish>
            <Publish Dialog="InstallDirDlg"
                     Control="Back"
                     Event="NewDialog"
                     Value="WelcomeDlg"
                     Order="2">1</Publish>
        </UI>

        <UIRef Id="WixUI_InstallDir" />

        <Directory Id="TARGETDIR" Name="SourceDir">
            <Directory Id="DesktopFolder" Name="Desktop">
                <Component Id="ApplicationShortcutDesktop" Guid="*">
                    <Shortcut Id="ApplicationDesktopShortcut" Name="RealityTap Haptics Studio" Description="Runs RealityTap Haptics Studio" Target="[!Path]" WorkingDirectory="INSTALLDIR" />
                    <RemoveFolder Id="DesktopFolder" On="uninstall" />
                    <RegistryValue Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Name="Desktop Shortcut" Type="integer" Value="1" KeyPath="yes" />
                </Component>
            </Directory>
            <Directory Id="$(var.PlatformProgramFilesFolder)" Name="PFiles">
                <Directory Id="INSTALLDIR" Name="RealityTap Haptics Studio"/>
            </Directory>
            <Directory Id="ProgramMenuFolder">
                <Directory Id="ApplicationProgramsFolder" Name="RealityTap Haptics Studio"/>
            </Directory>
        </Directory>

        <DirectoryRef Id="INSTALLDIR">
            <Component Id="RegistryEntries" Guid="*">
                <RegistryKey Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio">
                    <RegistryValue Name="InstallDir" Type="string" Value="[INSTALLDIR]" KeyPath="yes" />
                </RegistryKey>
                <!-- Change the Root to HKCU for perUser installations -->
</Component>
            <Component Id="Path" Guid="0147ef6a-fc30-5fd9-bd41-312c6ae15b1a" Win64="$(var.Win64)">
                <File Id="Path" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\release\realitytap_studio.exe" KeyPath="yes" Checksum="yes"/>
</Component>
            <Component Id="Id33b95b0738d4a9e9338b56adc0c9075" Guid="e4a92973-61be-4c93-9327-63b92388ddab" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Id33b95b0738d4a9e9338b56adc0c9075" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\librtcore.dll" /></Component><Component Id="I50bca965204d4880b99b65cd8bcffacf" Guid="d7848070-68d4-40c7-be7d-5f8ad4dd212b" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I50bca965204d4880b99b65cd8bcffacf" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\librtssl.dll" /></Component><Component Id="Ica1640ed1d8f4dc99c0c8a8ad9028c6a" Guid="c9a2f535-f895-46ae-864a-fccecaaa358f" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ica1640ed1d8f4dc99c0c8a8ad9028c6a" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\librtutils.dll" /></Component><Component Id="I8b092a6601ad4efd9dc45f03e6d8f8dc" Guid="3b82e76a-4029-47fa-97c3-b15ec647d29b" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I8b092a6601ad4efd9dc45f03e6d8f8dc" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\libgcc_s_seh-1.dll" /></Component><Component Id="Iac889c18118c41a3818cfcca94933aa4" Guid="5488ec52-7a0c-48a7-9853-28e4add5a4d5" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iac889c18118c41a3818cfcca94933aa4" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\libstdc++-6.dll" /></Component><Component Id="Icb78f49a62dc40ba840bab907b13916c" Guid="e870947d-8e85-4720-afb1-2c3fc3522417" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Icb78f49a62dc40ba840bab907b13916c" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\libwinpthread-1.dll" /></Component><Component Id="I2d3585dbb0964d27a06f6d152406b05d" Guid="0e049fce-5e03-4e9d-81e6-718cdf195b7b" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I2d3585dbb0964d27a06f6d152406b05d" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\ffmpeg.exe" /></Component><Component Id="I5100668b360a47d581121d0569902648" Guid="5c11189d-fa98-4a6d-b209-e17537899f29" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I5100668b360a47d581121d0569902648" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\ffprobe.exe" /></Component><Directory Id="I313575b63589407fb28b0440a16edc7d" Name="motors"><Component Id="Id87a0755397d4a07a19690ad39680ae3" Guid="0b091ca6-4a4f-4ea5-8b4a-c42ae45abe1f" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Id87a0755397d4a07a19690ad39680ae3" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\motors\LRA_0809_normal_170Hz.conf" /></Component><Component Id="If8d77e265910401d887f9f58a55952cf" Guid="651a5d75-1106-4f76-a136-e18e2c993b47" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_If8d77e265910401d887f9f58a55952cf" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\motors\LRA_0809_pro_170Hz.conf" /></Component><Component Id="I05b22caaa22745289611e9a1208bb148" Guid="6676f301-a124-4dcc-afc8-d1bb1d5c0a1a" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I05b22caaa22745289611e9a1208bb148" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\motors\LRA_0916_normal_170Hz.conf" /></Component></Directory>
            <Component Id="CMP_UninstallShortcut" Guid="*">

                <Shortcut Id="UninstallShortcut"
						  Name="Uninstall RealityTap Haptics Studio"
						  Description="Uninstalls RealityTap Haptics Studio"
						  Target="[System64Folder]msiexec.exe"
						  Arguments="/x [ProductCode]" />

				<RemoveFolder Id="INSTALLDIR"
							  On="uninstall" />

				<RegistryValue Root="HKCU"
							   Key="Software\AWA\RealityTap Haptics Studio"
							   Name="Uninstaller Shortcut"
							   Type="integer"
							   Value="1"
							   KeyPath="yes" />
            </Component>
        </DirectoryRef>

        <DirectoryRef Id="ApplicationProgramsFolder">
            <Component Id="ApplicationShortcut" Guid="*">
                <Shortcut Id="ApplicationStartMenuShortcut"
                    Name="RealityTap Haptics Studio"
                    Description="Runs RealityTap Haptics Studio"
                    Target="[!Path]"
                    Icon="ProductIcon"
                    WorkingDirectory="INSTALLDIR">
                    <ShortcutProperty Key="System.AppUserModel.ID" Value="com.awa.realitytap.desktop"/>
                </Shortcut>
                <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall"/>
                <RegistryValue Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Name="Start Menu Shortcut" Type="integer" Value="1" KeyPath="yes"/>
           </Component>
        </DirectoryRef>

<Feature
                Id="MainProgram"
                Title="Application"
                Description="!(loc.InstallAppFeature)"
                Level="1"
                ConfigurableDirectory="INSTALLDIR"
                AllowAdvertise="no"
                Display="expand"
                Absent="disallow">

            <ComponentRef Id="RegistryEntries"/>

<ComponentRef Id="Id33b95b0738d4a9e9338b56adc0c9075"/>
<ComponentRef Id="I50bca965204d4880b99b65cd8bcffacf"/>
<ComponentRef Id="Ica1640ed1d8f4dc99c0c8a8ad9028c6a"/>
<ComponentRef Id="I8b092a6601ad4efd9dc45f03e6d8f8dc"/>
<ComponentRef Id="Iac889c18118c41a3818cfcca94933aa4"/>
<ComponentRef Id="Icb78f49a62dc40ba840bab907b13916c"/>
<ComponentRef Id="I2d3585dbb0964d27a06f6d152406b05d"/>
<ComponentRef Id="I5100668b360a47d581121d0569902648"/>
<ComponentRef Id="Id87a0755397d4a07a19690ad39680ae3"/>
<ComponentRef Id="If8d77e265910401d887f9f58a55952cf"/>
<ComponentRef Id="I05b22caaa22745289611e9a1208bb148"/>

            <Feature Id="ShortcutsFeature"
                Title="Shortcuts"
                Level="1">
                <ComponentRef Id="Path"/>
                <ComponentRef Id="CMP_UninstallShortcut" />
                <ComponentRef Id="ApplicationShortcut" />
                <ComponentRef Id="ApplicationShortcutDesktop" />
            </Feature>

            <Feature
                Id="Environment"
                Title="PATH Environment Variable"
                Description="!(loc.PathEnvVarFeature)"
                Level="1"
                Absent="allow">
            <ComponentRef Id="Path"/>
</Feature>
        </Feature>

        <Feature Id="External" AllowAdvertise="no" Absent="disallow">
</Feature>

        <!-- WebView2 -->
        <Property Id="WVRTINSTALLED">
            <RegistrySearch Id="WVRTInstalledSystem" Root="HKLM" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw" Win64="no" />
            <RegistrySearch Id="WVRTInstalledUser" Root="HKCU" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw"/>
        </Property>

        <CustomAction Id='DownloadAndInvokeBootstrapper' Directory="INSTALLDIR" Execute="deferred" ExeCommand='powershell.exe -NoProfile -windowstyle hidden try [\{] [\[]Net.ServicePointManager[\]]::SecurityProtocol = [\[]Net.SecurityProtocolType[\]]::Tls12 [\}] catch [\{][\}]; Invoke-WebRequest -Uri "https://go.microsoft.com/fwlink/p/?LinkId=2124703" -OutFile "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" ; Start-Process -FilePath "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" -ArgumentList (&apos;/silent&apos;, &apos;/install&apos;) -Wait' Return='check'/>
        <InstallExecuteSequence>
            <Custom Action='DownloadAndInvokeBootstrapper' Before='InstallFinalize'>
                <![CDATA[NOT(REMOVE OR WVRTINSTALLED)]]>
            </Custom>
        </InstallExecuteSequence>

        <!-- Embedded webview bootstrapper mode -->

        <!-- Embedded offline installer -->



        <InstallExecuteSequence>
          <Custom Action="LaunchApplication" After="InstallFinalize">AUTOLAUNCHAPP AND NOT Installed</Custom>
        </InstallExecuteSequence>

        <SetProperty Id="ARPINSTALLLOCATION" Value="[INSTALLDIR]" After="CostFinalize"/>
    </Product>
</Wix>
